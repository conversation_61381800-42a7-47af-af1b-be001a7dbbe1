package com.workly.app.services

import android.content.Context
import com.workly.app.data.model.*
import com.workly.app.data.repository.SettingsRepository
import com.workly.app.data.repository.ShiftRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.first
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ShiftRotationServiceImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val settingsRepository: SettingsRepository,
    private val shiftRepository: ShiftRepository,
    private val notificationService: NotificationService,
    private val reminderSchedulingService: ReminderSchedulingService
) : ShiftRotationService {

    private val rotationStrategy: RotationStrategy = DefaultRotationStrategy()

    override suspend fun checkAndRotateShift(): ShiftRotationResult {
        try {
            val settings = settingsRepository.getUserSettings().first()
            val rotationConfig = settings.rotationConfig
            
            if (rotationConfig == null || rotationConfig.rotationShifts.isEmpty()) {
                return ShiftRotationResult(
                    success = false,
                    previousShiftId = null,
                    newShiftId = null,
                    rotationDate = LocalDate.now(),
                    rotationType = RotationType.AUTOMATIC,
                    message = "Chưa cấu hình xoay ca",
                    errors = listOf("Rotation config not found or empty")
                )
            }
            
            val context = buildRotationContext(rotationConfig)
            
            // Kiểm tra xem có đến hạn xoay ca không
            if (!rotationStrategy.isRotationDue(context)) {
                return ShiftRotationResult(
                    success = false,
                    previousShiftId = settings.activeShiftId,
                    newShiftId = settings.activeShiftId,
                    rotationDate = LocalDate.now(),
                    rotationType = RotationType.AUTOMATIC,
                    message = "Chưa đến hạn xoay ca",
                    nextRotationDate = calculateNextRotationDate(rotationConfig)
                )
            }
            
            // Validate rotation
            val validation = rotationStrategy.validateRotation(context)
            if (!validation.isValid) {
                return ShiftRotationResult(
                    success = false,
                    previousShiftId = settings.activeShiftId,
                    newShiftId = null,
                    rotationDate = LocalDate.now(),
                    rotationType = RotationType.AUTOMATIC,
                    message = "Validation failed",
                    errors = validation.errors
                )
            }
            
            // Tính toán ca tiếp theo
            val calculation = rotationStrategy.calculateNextRotation(context)
            
            // Thực hiện xoay ca
            val result = performRotation(
                previousShiftId = settings.activeShiftId,
                newShiftId = calculation.nextShiftId,
                rotationType = RotationType.AUTOMATIC,
                rotationDate = calculation.rotationDate
            )
            
            if (result.success) {
                // Gửi thông báo
                val newShift = shiftRepository.getShiftById(calculation.nextShiftId)
                if (newShift != null) {
                    sendRotationNotification(newShift, calculation.rotationDate)
                }
                
                // Cập nhật nhắc nhở
                reminderSchedulingService.onActiveShiftChanged(calculation.nextShiftId)
            }
            
            return result
            
        } catch (e: Exception) {
            return ShiftRotationResult(
                success = false,
                previousShiftId = null,
                newShiftId = null,
                rotationDate = LocalDate.now(),
                rotationType = RotationType.AUTOMATIC,
                message = "Lỗi hệ thống: ${e.message}",
                errors = listOf(e.message ?: "Unknown error")
            )
        }
    }

    override suspend fun calculateNextShift(
        currentShiftId: String,
        rotationConfig: RotationConfig
    ): String? {
        val currentIndex = rotationConfig.rotationShifts.indexOf(currentShiftId)
        if (currentIndex == -1) return null
        
        val nextIndex = (currentIndex + 1) % rotationConfig.rotationShifts.size
        return rotationConfig.rotationShifts[nextIndex]
    }

    override suspend fun isRotationDue(rotationConfig: RotationConfig): Boolean {
        val context = buildRotationContext(rotationConfig)
        return rotationStrategy.isRotationDue(context)
    }

    override suspend fun getCurrentRotationInfo(): RotationInfo? {
        val settings = settingsRepository.getUserSettings().first()
        val rotationConfig = settings.rotationConfig ?: return null
        val currentShift = settings.activeShiftId?.let { 
            shiftRepository.getShiftById(it) 
        } ?: return null
        
        val nextShiftId = calculateNextShift(currentShift.id, rotationConfig)
        val nextShift = nextShiftId?.let { shiftRepository.getShiftById(it) }
        
        val lastRotationDate = rotationConfig.rotationLastAppliedDate?.let { 
            LocalDate.parse(it) 
        }
        
        val daysSinceLastRotation = lastRotationDate?.let {
            java.time.temporal.ChronoUnit.DAYS.between(it, LocalDate.now()).toInt()
        } ?: 0
        
        val rotationIntervalDays = getRotationIntervalDays(rotationConfig.rotationFrequency)
        val daysUntilNextRotation = maxOf(0, rotationIntervalDays - daysSinceLastRotation)
        val rotationProgress = if (rotationIntervalDays > 0) {
            daysSinceLastRotation.toDouble() / rotationIntervalDays
        } else 0.0
        
        return RotationInfo(
            currentShift = currentShift,
            nextShift = nextShift,
            rotationConfig = rotationConfig,
            isRotationEnabled = true, // From settings
            daysSinceLastRotation = daysSinceLastRotation,
            daysUntilNextRotation = daysUntilNextRotation,
            rotationProgress = rotationProgress.coerceIn(0.0, 1.0),
            lastRotationDate = lastRotationDate,
            nextRotationDate = calculateNextRotationDate(rotationConfig)
        )
    }

    override suspend fun updateRotationConfig(config: RotationConfig) {
        val settings = settingsRepository.getUserSettings().first()
        val updatedSettings = settings.copy(rotationConfig = config)
        settingsRepository.updateUserSettings(updatedSettings)
    }

    override suspend fun setRotationEnabled(enabled: Boolean) {
        // Implementation would update settings
        // For now, rotation is always enabled if config exists
    }

    override suspend fun resetRotationCycle() {
        val settings = settingsRepository.getUserSettings().first()
        val rotationConfig = settings.rotationConfig
        
        if (rotationConfig != null) {
            val resetConfig = rotationConfig.copy(
                currentRotationIndex = 0,
                rotationLastAppliedDate = LocalDate.now().toString()
            )
            updateRotationConfig(resetConfig)
        }
    }

    override suspend fun manualRotateShift(): ShiftRotationResult {
        val settings = settingsRepository.getUserSettings().first()
        val rotationConfig = settings.rotationConfig
        
        if (rotationConfig == null || rotationConfig.rotationShifts.isEmpty()) {
            return ShiftRotationResult(
                success = false,
                previousShiftId = null,
                newShiftId = null,
                rotationDate = LocalDate.now(),
                rotationType = RotationType.MANUAL,
                message = "Chưa cấu hình xoay ca"
            )
        }
        
        val nextShiftId = calculateNextShift(
            settings.activeShiftId ?: "",
            rotationConfig
        )
        
        if (nextShiftId == null) {
            return ShiftRotationResult(
                success = false,
                previousShiftId = settings.activeShiftId,
                newShiftId = null,
                rotationDate = LocalDate.now(),
                rotationType = RotationType.MANUAL,
                message = "Không thể xác định ca tiếp theo"
            )
        }
        
        return performRotation(
            previousShiftId = settings.activeShiftId,
            newShiftId = nextShiftId,
            rotationType = RotationType.MANUAL,
            rotationDate = LocalDate.now()
        )
    }

    override suspend fun getRotationHistory(limit: Int): List<RotationHistoryItem> {
        // Implementation would fetch from database
        // For now, return empty list
        return emptyList()
    }

    override suspend fun predictRotationSchedule(days: Int): List<RotationPrediction> {
        val settings = settingsRepository.getUserSettings().first()
        val rotationConfig = settings.rotationConfig ?: return emptyList()
        
        val predictions = mutableListOf<RotationPrediction>()
        val startDate = LocalDate.now()
        val rotationIntervalDays = getRotationIntervalDays(rotationConfig.rotationFrequency)
        
        var currentDate = startDate
        var currentShiftIndex = rotationConfig.currentRotationIndex
        
        for (i in 0 until days) {
            val isRotationDay = (i % rotationIntervalDays) == 0 && i > 0
            
            if (isRotationDay) {
                currentShiftIndex = (currentShiftIndex + 1) % rotationConfig.rotationShifts.size
            }
            
            val shiftId = rotationConfig.rotationShifts[currentShiftIndex]
            val shift = shiftRepository.getShiftById(shiftId)
            
            predictions.add(
                RotationPrediction(
                    date = currentDate,
                    shiftId = shiftId,
                    shiftName = shift?.name ?: "Unknown",
                    rotationIndex = currentShiftIndex,
                    isRotationDay = isRotationDay,
                    confidence = 1.0
                )
            )
            
            currentDate = currentDate.plusDays(1)
        }
        
        return predictions
    }

    override suspend fun sendRotationReminder(nextShift: Shift, rotationDate: LocalDate) {
        notificationService.showInstantNotification(
            title = "Xoay ca làm việc",
            message = "Ca làm việc đã chuyển sang: ${nextShift.name}",
            channelId = "shift_rotation"
        )
    }
    
    private suspend fun performRotation(
        previousShiftId: String?,
        newShiftId: String,
        rotationType: RotationType,
        rotationDate: LocalDate
    ): ShiftRotationResult {
        try {
            // Cập nhật active shift
            val settings = settingsRepository.getUserSettings().first()
            val rotationConfig = settings.rotationConfig!!
            
            val newIndex = rotationConfig.rotationShifts.indexOf(newShiftId)
            val updatedConfig = rotationConfig.copy(
                currentRotationIndex = newIndex,
                rotationLastAppliedDate = rotationDate.toString()
            )
            
            val updatedSettings = settings.copy(
                activeShiftId = newShiftId,
                rotationConfig = updatedConfig
            )
            
            settingsRepository.updateUserSettings(updatedSettings)
            
            // Lưu lịch sử (implementation would save to database)
            saveRotationHistory(
                previousShiftId = previousShiftId,
                newShiftId = newShiftId,
                rotationType = rotationType,
                rotationDate = rotationDate
            )
            
            return ShiftRotationResult(
                success = true,
                previousShiftId = previousShiftId,
                newShiftId = newShiftId,
                rotationDate = rotationDate,
                rotationType = rotationType,
                message = "Xoay ca thành công",
                nextRotationDate = calculateNextRotationDate(updatedConfig)
            )
            
        } catch (e: Exception) {
            return ShiftRotationResult(
                success = false,
                previousShiftId = previousShiftId,
                newShiftId = null,
                rotationDate = rotationDate,
                rotationType = rotationType,
                message = "Lỗi khi xoay ca: ${e.message}",
                errors = listOf(e.message ?: "Unknown error")
            )
        }
    }
    
    private suspend fun buildRotationContext(rotationConfig: RotationConfig): RotationContext {
        val settings = settingsRepository.getUserSettings().first()
        val currentShift = settings.activeShiftId?.let { 
            shiftRepository.getShiftById(it) 
        }
        val allShifts = shiftRepository.getAllShifts().first()
        
        return RotationContext(
            currentDate = LocalDate.now(),
            currentShift = currentShift,
            availableShifts = allShifts,
            rotationConfig = rotationConfig,
            advancedConfig = AdvancedRotationConfig(), // Default
            userSettings = settings,
            rotationHistory = emptyList() // Would fetch from database
        )
    }
    
    private fun calculateNextRotationDate(rotationConfig: RotationConfig): LocalDate {
        val lastAppliedDate = rotationConfig.rotationLastAppliedDate?.let { 
            LocalDate.parse(it) 
        } ?: LocalDate.now()
        
        val intervalDays = getRotationIntervalDays(rotationConfig.rotationFrequency)
        return lastAppliedDate.plusDays(intervalDays.toLong())
    }
    
    private fun getRotationIntervalDays(frequency: RotationFrequency): Int {
        return when (frequency) {
            RotationFrequency.DAILY -> 1
            RotationFrequency.WEEKLY -> 7
            RotationFrequency.BIWEEKLY -> 14
            RotationFrequency.TRIWEEKLY -> 21
            RotationFrequency.MONTHLY -> 30
            RotationFrequency.CUSTOM -> 7 // Default
        }
    }
    
    private suspend fun saveRotationHistory(
        previousShiftId: String?,
        newShiftId: String,
        rotationType: RotationType,
        rotationDate: LocalDate
    ) {
        // Implementation would save to database
        val historyItem = RotationHistoryItem(
            id = UUID.randomUUID().toString(),
            rotationDate = rotationDate,
            fromShiftId = previousShiftId ?: "",
            toShiftId = newShiftId,
            fromShiftName = previousShiftId?.let { 
                shiftRepository.getShiftById(it)?.name 
            } ?: "",
            toShiftName = shiftRepository.getShiftById(newShiftId)?.name ?: "",
            rotationType = rotationType,
            createdAt = LocalDateTime.now()
        )
        
        // Save to database (not implemented yet)
    }
    
    private suspend fun sendRotationNotification(shift: Shift, rotationDate: LocalDate) {
        notificationService.showInstantNotification(
            title = "Xoay ca làm việc",
            message = "Ca làm việc đã chuyển sang: ${shift.name} từ ngày ${rotationDate}",
            channelId = "shift_rotation"
        )
    }
}
